# 项目相关配置
ruoyi:
  name: RuoYi
  version: 1.0.0
  copyrightYear: 2024
  demoEnabled: true
  profile: D:/ruoyi/uploadPath

# 开发环境配置
server:
  port: 8080
  servlet:
    context-path: /
  tomcat:
    uri-encoding: UTF-8
    accept-count: 1000
    threads:
      max: 800
      min-spare: 100

# 数据源配置
spring:
  datasource:
    driver-class-name: com.mysql.cj.jdbc.Driver
    url: **************************************************************************************************************************************************
    username: root
    password: 123456

# MyBatis 配置
mybatis:
  mapper-locations: classpath*:mapper/**/*.xml
  type-aliases-package: com.ruoyi.module.*.domain
  configuration:
    map-underscore-to-camel-case: true
    cache-enabled: false
    call-setters-on-nulls: true
    jdbc-type-for-null: 'null'

# MyBatis-Plus 配置已移除，使用纯 MyBatis

# 日志配置
logging:
  level:
    com.ruoyi: debug
    org.springframework: warn