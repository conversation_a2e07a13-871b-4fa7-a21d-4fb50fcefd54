package com.ruoyi.module.user.aop;

import com.ruoyi.framework.utils.Privilege;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Before;
import org.aspectj.lang.annotation.Pointcut;
import org.aspectj.lang.reflect.MethodSignature;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import java.lang.reflect.Method;

/**
 * 权限切面类，用于扫描controller包下所有接口并获取@Privilege注解的值
 */
@Aspect
@Component
public class PrivilegeAOP {

    private static final Logger logger = LoggerFactory.getLogger(PrivilegeAOP.class);

    /**
     * 定义切入点，拦截user.controller包下所有类的所有方法
     */
    @Pointcut("execution(* com.ruoyi.module.user.controller..*(..))")
    public void privilegePointcut() {
    }

    /**
     * 前置通知，用于获取方法上的@Privilege注解值
     *
     * @param joinPoint 连接点
     */
    @Before("privilegePointcut()")
    public void before(JoinPoint joinPoint) {
        // 获取目标方法
        MethodSignature signature = (MethodSignature) joinPoint.getSignature();
        Method method = signature.getMethod();

        // 获取方法上的@Privilege注解
        Privilege privilegeAnnotation = method.getAnnotation(Privilege.class);
        if (privilegeAnnotation != null) {
            logger.info("方法 {} 上找到了 @Privilege 注解", 
                joinPoint.getTarget().getClass().getSimpleName() + "." + method.getName());
            // 这里可以处理权限逻辑
            // 由于当前Privilege注解没有属性，此处仅作演示
        } else {
            logger.debug("方法 {} 上没有 @Privilege 注解", 
                joinPoint.getTarget().getClass().getSimpleName() + "." + method.getName());
        }
    }
}