<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.module.user.mapper.UserMapper">

    <resultMap type="com.ruoyi.module.user.domain.User" id="UserResult">
        <id     property="id"           jdbcType="BIGINT" column="id"           />
        <result property="username"     jdbcType="VARCHAR" column="username"     />
        <result property="age"          jdbcType="INTEGER" column="age"          />
        <result property="password"     jdbcType="VARCHAR" column="password"     />
    </resultMap>

    <sql id="selectUserVo">
        select id, username, age, password
        from user
    </sql>

    <select id="selectUserList" parameterType="com.ruoyi.module.user.domain.User" resultMap="UserResult">
        <include refid="selectUserVo"/>
        <where>
            <if test="id != null and id != 0">
                AND id = #{id}
            </if>
            <if test="username != null and username != ''">
                AND username like concat('%', #{username}, '%')
            </if>
            <if test="age != null">
                AND age = #{age}
            </if>
        </where>
        ORDER BY id DESC
    </select>

    <select id="selectUserByUsername" parameterType="String" resultMap="UserResult">
        <include refid="selectUserVo"/>
        where username = #{username}
    </select>

    <select id="selectUserById" parameterType="Long" resultMap="UserResult">
        <include refid="selectUserVo"/>
        where id = #{id}
    </select>

    <select id="checkUsernameUnique" parameterType="String" resultMap="UserResult">
        <include refid="selectUserVo"/>
        where username = #{username} limit 1
    </select>

    <select id="countUsers" resultType="int">
        select count(*) from user
    </select>

    <insert id="insertUser" parameterType="com.ruoyi.module.user.domain.User" useGeneratedKeys="true" keyProperty="id">
        insert into user(
            <if test="username != null and username != ''">username,</if>
            <if test="age != null">age</if>
        )values(
            <if test="username != null and username != ''">#{username},</if>
            <if test="age != null">#{age}</if>
        )
    </insert>

    <update id="updateUser" parameterType="com.ruoyi.module.user.domain.User">
        update user
        <set>
            <if test="username != null and username != ''">username = #{username},</if>
            <if test="age != null">age = #{age}</if>
        </set>
        where id = #{id}
    </update>

    <delete id="deleteUserById" parameterType="Long">
        delete from user where id = #{id}
    </delete>

    <delete id="deleteUserByIds" parameterType="String">
        delete from user where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

</mapper>
