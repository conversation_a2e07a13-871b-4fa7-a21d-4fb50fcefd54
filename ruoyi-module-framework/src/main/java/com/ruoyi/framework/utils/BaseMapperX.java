package com.ruoyi.framework.utils;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;

import java.io.Serializable;
import java.util.Collection;
import java.util.List;

/**
 * 自定义 Mapper 基础接口，继承 BaseMapper 并扩展常用方法
 *
 * @param <T> 实体类类型
 * <AUTHOR>
 */
public interface BaseMapperX<T> extends BaseMapper<T> {

    /**
     * 批量插入
     *
     * @param entities 实体列表
     * @return 插入条数
     */
    default int insertBatch(@Param("entities") Collection<T> entities) {
        return 0;
    }

    /**
     * 批量更新
     *
     * @param entities 实体列表
     * @return 更新条数
     */
    default int updateBatch(@Param("entities") Collection<T> entities) {
        return 0;
    }

    /**
     * 根据 ID 删除
     *
     * @param id 主键 ID
     * @return 删除条数
     */
    default int deleteById(@Param("id") Long id) {
        return 0;
    }

    /**
     * 根据 ID 批量删除
     *
     * @param ids 主键 ID 列表
     * @return 删除条数
     */
    default int deleteBatchIds(@Param("ids") Collection<?> ids) {
        return 0;
    }

    /**
     * 根据 ID 查询
     *
     * @param id 主键 ID
     * @return 实体对象
     */
    default T selectById(@Param("id") Long id) {
        return null;
    }

    /**
     * 根据 ID 批量查询
     *
     * @param ids 主键 ID 列表
     * @return 实体对象列表
     */
    default List<T> selectBatchIds(@Param("ids") Collection<? extends Serializable> ids) {
        return null;
    }

    /**
     * 查询所有记录
     *
     * @return 实体对象列表
     */
    default List<T> selectAll() {
        return null;
    }

    /**
     * 根据条件查询单条记录
     *
     * @param entity 查询条件
     * @return 实体对象
     */
    default T selectOne(@Param("entity") T entity) {
        return null;
    }

    /**
     * 根据条件查询列表
     *
     * @param entity 查询条件
     * @return 实体对象列表
     */
    default List<T> selectList(@Param("entity") T entity) {
        return null;
    }
}